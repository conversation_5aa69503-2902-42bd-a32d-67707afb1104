import numpy as np
from surrogate_model import kriging2
import time
from scipy import sparse
from scipy.sparse import linalg
from optimization_algorithm import XMMA
from sampling import lhs
from scipy.io import loadmat
import scipy
from pyKriging.krige import kriging
import mma
import mph

def st(x, nelx, nely, V0, c):
    V = np.sum(x)/(nelx*nely)
    Y1 = 0
    if V > V0:
        Y1 = (V-V0)*2*10**(np.floor(np.log10(np.abs(c)+0.001))+1)
    ys = Y1
    return ys, V

def pre2(Xnew, model):
    Beta = model.Beta
    Rn = model.Rn
    X = model.X
    S = model.S
    Y = model.Y
    sita = model.sita
    diff_x = (Xnew[:, 0] - S[:, 0].H)**2
    diff_y = (Xnew[:, 1] - S[:, 1].H)**2
    r = np.exp(-sita[0] * diff_x - sita[1]* diff_y)
    r = r.H
    Ynew = Beta + r.H@Rn@(Y - X @ Beta)
    return Ynew

def KF(S, Y, sita, nelx, nely, xs):
    model = kriging2(S, Y, sita)
    model.get_model()
    x00 = np.empty(shape=(nelx*nely, 2))
    xx00 = np.empty(shape=(nelx*nely, 2))
    i = 0
    for ely in range(1, nely+1):
        for elx in range(1, nelx+1):
            x00[i, :] = np.array([elx-0.5, ely-0.5])
            xx00[i, :] = np.array([elx, ely])
            i += 1
    yg = pre2(x00, model)
    fyg = 1./(1+np.exp(-xs*(yg*2-1)*10))
    fyg[fyg < 0.001] = 0.001
    xg = x00
    return fyg, yg, xg, model


def pre20(Xnew, model, Beta, dmY):
    h, l = Xnew.shape
    Rn = model.Rn
    X = model.X
    S = model.S
    Y = dmY
    sita = model.sita
    diff_x = (Xnew[:, 0] - S[:, 0]) ** 2
    diff_y = (Xnew[:, 1] - S[:, 1]) ** 2
    r = np.exp(-sita[0]*diff_x - sita[1]*diff_y)
    r = r.H
    Ynew = <EMAIL>(shape=(r.shape[1], 1)) + (r.H@Rn)@(Y-X@Beta)
    return Ynew

def lk():
    E = 1
    nu = 0.3
    k = np.array(
        [1/2-nu/6, 1/8+nu/8, -1/4-nu/12, -1/8+3*nu/8,
         -1/4+nu/12, -1/8-nu/8, nu/6, 1/8-3*nu/8]
    )
    KE = E/(1-nu**2)@np.array([k[0], k[1], k[2], k[3], k[4], k[5], k[6], k[7],
                      k[1], k[0], k[7], k[6], k[5], k[4], k[3], k[2],
                      k[2], k[7], k[0], k[5], k[6], k[3], k[4], k[1],
                      k[3], k[6], k[5], k[0], k[7], k[2], k[1], k[4],
                      k[4], k[5], k[6], k[7], k[0], k[1], k[2], k[3],
                      k[5], k[4], k[3], k[2], k[1], k[0], k[7], k[6],
                      k[6], k[3], k[4], k[1], k[2], k[7], k[0], k[5],
                      k[7], k[2], k[1], k[4], k[3], k[6], k[5], k[0]])
    return KE

def zl(x0):
    data = x0
    y = data[:, 0]
    x = data[:, 1]
    values = data[:, 2]
    
    scale_factor = 0.25
    x_scaled = x*scale_factor
    y_scaled = y*scale_factor

    scaled_data = np.column_stack((y_scaled, x_scaled, values))
    array_data = []
    for i in range(3):
        for j in range(2):
            y_offset = y_scaled + j*25
            x_offset = x_scaled + i*25
            offset_data = np.column_stack((y_offset, x_offset, values))
            array_data.append(offset_data)
    return np.array(array_data)
    
def FE(x):
    h, l = x.shape
    z = 0
    wz = []
    for i in range(h):
        for j in range(l):
            wz.append([j, i, x[i, j]])
    wz = np.array(wz)
    wzl = wz[:, 2]
    wzl[wzl < 0.02] = 0.02
    wz[:, 2] = wzl
    wz0 = zl(wz)
    np.savetxt('x.txt', wz0, delimiter=',')
    try:
        client = mph.start()
        model = client.load('mx2.mph')
        model.solve()
        U = model.evaluate('u')
        U = -U
    except Exception as e:
        print(f"Error: {e}")
    return U

def st(x, nelx, nely, V0, c):
    V = np.sum(x)/(nelx*nely)
    Y1 = 0
    if V > V0:
        Y1 = (V-V0)*2*10**(np.floor(np.log10(np.abs(c)+0.001))+1)
    ys = Y1
    return ys, V

if __name__ == "__main__":
    nelx = 100
    nely = 100
    volfrac = 0.2
    penal = 3
    N = 2
    sy = 0
    qs0 = 1
    cd = 2
    alf = 0.2
    low = 0.01
    up = 1
    jg1 = 14
    jg2 = 14
    pd1 = 0
    pdz = 2.5
    x1 = np.array(range(0, nelx, jg1))
    x2 = np.array(range(0, nely, jg2))
    x12 = 0
    S = []
    for i in range(len(x1)):
        for j in range(len(x2)):
            x12 += 1
            S.append([x1[i], x2[j]])
    S = np.array(S)
    Y = np.array([[volfrac]]*x12)
    c = 0
    sta = np.array([1/(jg1*1)**2, 1/(jg2*1)**2])
    theta = np.array([[sta]*N])

    N0 = x12
    ytheta = np.array([0.2e0]*N0)
    ylob = np.array([1e-4]*N0)
    yupb = np.array([1e1]*N0)
    Cy = N0*cd

    for qs in range(qs0, 12):
        Y0 = 0
        S0 = np.zeros((N0, Cy))
        Cy0 = Cy
        if sy == 0:
            S0 = lhs(Cy, N0, 0, 1)
            if qs > 1:
                alf += 0.2
                S0[0, :] = loadmat("xx0.mat")
            else:
                S0[0, :] = volfrac
            ylow = max(S0[0, :] - 0.2*0.95**(qs-1), low)
            yup = min(S0[0, :]+0.2*0.95**(qs-1), up)
            S0[1:, :] = S0[1:, :]*(yup-ylow) + ylow
            Y0 = []
            Vx = []
            Vv = []
            for j in range(Cy):
                Y = S0[j, :]
                x, fyg, yg, xg, model = KF(S, Y, theta, nelx, nely, alf)
                U = FE(x**penal)
                c = U
                ys, Vx0 = st(x, nelx, nely, volfrac, c)
                c += ys
                Y0.append(c)
                Vx.append(Vx0)
                Vv.append(ys)
            Y0 = np.array(Y0)
            Vx = np.array(Vx)
            Vv = np.array(Vv)
            scipy.io.savemat("SY0.mat", {"S0":S0, "Y0":Y0, "alf":alf})
        else:
            SY0 = loadmat("SY0.mat")
            ylow = max(S0[0, :]-0.2*0.95**(qs-1), low)
            yup = min(S0[0, :]+0.2*0.95**(qs-1), up)
            sy = 0
        dmodel = model.model
        zzt1 = 0
        pdcs = 0
        while True:
            m = 1
            n = N0
            zzt1 += 1
            loop1 = 0
            minY = np.min(Y0)
            wzp = np.where(Y0 == minY)[0]
            xval = S0[wzp, :].T
            S00 = S0[wzp, :]
            sh, sl = S00.shape
            if sh > 1: break
        change = 1
        while change > 0.0001 and loop1 < 50:
            loop1 += 1
            c = dmodel.predict(S00)
            dc = model.kriging_gradient(S00)
            df0dx = dc
            fval = np.array([1])
            dfdx = np.zeros((m, n))
            if loop1 == 1:
                a = np.zeros(m)
                cc = 10000*np.ones(m)
                d = np.zeros(m)
                a0 = 1
                xold1 = xval
                xold2 = xval
                low0 = ylow.T
                up0 = yup.T
                xmin = ylow.T
                xmax = yup.T
            xmma,ymma,zmma,lam,xsi,eta,mu,zet,s,low0,up0 = mma.mmasub(m,n,loop1,xval,xmin,xmax,xold1,xold2,c,df0dx,fval,dfdx,low0,up0,a0,a,cc,d)
            xold2 = xold1
            xold1 = xval
            xval = xmma
            S00 = xmma.T
            if loop1 > 2:
                change = np.max(np.abs(xmma-xold1))
            print(f" ite: {loop1:4d} Ob: {c:10.4f} ch: {change:10.4f}qs {qs:10.4f}")
        Shs = S00
        x, fyg, yg, xg, model = KF(S, Y, theta, nelx, nely, alf)
        U = FE(x**penal)
        c = U
        ys, Vx0 = st(x, nelx, nely, volfrac, c)
        c += ys
        Y1 = c
        Cy0 = Cy + zzt1
        Y0[Cy0] = Y1
        Vv[Cy0] = ys
        Vx[Cy0] = Vx0
        S0[Cy0] = S00

        if Y1 < np.min(Y[:-2]):
            


    loop = 0
    loopz = 0
    fyg, yg, xg, model=KF(S, Y, theta, nelx, nely, alf)

    i0 = len(Y)
    DY = np.eye(i0)
    second = model.s00@DY
    Beta = np.linalg.solve(model.first, second)
    dmY = DY
    start = time.time()
    dydye = pre20(xg, model, Beta, dmY)
    end = time.time()
    x = fyg.reshape(nelx, nely)
    while alf < pdz:
        loop += 1
        loopz += 1
        U = FE(nelx, nely, x, penal)
        KE = lk()
        cold = c
        c = 0
        C=[]
        for ely in range(1, nely+1):
            for elx in range(1, nelx+1):
                n1 = (nely+1)*(elx-1)+ely
                n2 = (nely+1)*elx+ely
                Ue = U[[2*n1-1, 2*n1, 2*n2-1, 2*n2, 2*n2+1, 2*n2+2, 2*n1+1, 2*n1+2], 0]
                c = c + x[ely-1, elx-1]**penal@Ue.T@KE@Ue
                C.append(c)
        dc = np.zeros(nelx*nely, i0)
        nn = 0
        df = []
        hd = []
        for ely in range(1, nely+1):
            for elx in range(1, nely+1):
                n1 = (nely+1)*(elx-1)+ely
                n2 = (nely+1)*elx+ely
                Ue = U[[2*n1-1, 2*n1, 2*n2-1, 2*n2, 2*n2+1, 2*n2+2, 2*n1+1, 2*n1+2], 0]
                df.append(20*alf/(1+np.exp(-alf*yg[nn]*2-1)*10)**2*np.exp(-alf*(yg[nn]*2-1)*10))
                hd.append(df[nn]*dydye[nn])
                dc[nn]=dc[nn]-(penal*x[ely-1, elx-1]**penal@Ue.T@KE@Ue)*hd[nn]
                nn += 1
        dc = np.sum(dc, axis=1).T
        st = [np.sum(x)-volfrac*nelx*nely]
        dst = [np.sum(hd)]*i0

        if loopz == 0:
            xold1 = Y
            xold2 = Y
            lowi = low
            upi = up
        xnew, lowi, upi = XMMA(Y, c, dc, st, dst, low, up, loopz, xold1, xold2, lowi, upi)


        