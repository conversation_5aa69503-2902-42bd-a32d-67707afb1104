import numpy as np
import random
import sampling
import utils
import torch
import scipy
from scipy import sparse

def ga(bounds, obj_func, pop_size=50, n_generations=100, crossover_rate=0.8, mutation_rate=0.1, elite_size=2):
    """
    遗传算法优化方法

    参数:
        bounds: 搜索空间边界，形式为 [(x1_min, x1_max), (x2_min, x2_max), ...]
        obj_func: 目标函数，接受一个numpy数组作为输入并返回一个标量值
        pop_size: 种群大小
        n_generations: 迭代代数
        crossover_rate: 交叉概率
        mutation_rate: 变异概率
        elite_size: 精英个体数量

    返回:
        best_solution: 最优解
        best_fitness: 最优适应度值
    """
    def _tournament_selection(fitness, k=3):
        """锦标赛选择"""
        indices = np.random.randint(0, len(fitness), k)
        selected_idx = indices[np.argmax(fitness[indices])]
        return selected_idx

    def _crossover(parent1, parent2):
        """模拟二进制交叉(SBX)"""
        eta = 1  # 分布指数
        child = np.zeros_like(parent1)

        for i in range(len(parent1)):
            # 生成随机数
            u = np.random.random()

            # 计算beta值
            if u <= 0.5:
                beta = (2 * u) ** (1 / (eta + 1))
            else:
                beta = (1 / (2 * (1 - u))) ** (1 / (eta + 1))

            # 计算子代
            child[i] = 0.5 * ((1 + beta) * parent1[i] + (1 - beta) * parent2[i])

        return child

    def _mutation(individual, bounds):
        """多项式变异"""
        eta = 20  # 分布指数
        mutated = individual.copy()

        for i in range(len(individual)):
            r = np.random.random()
            if r < 0.5:
                delta = (2 * r) ** (1 / (eta + 1)) - 1
            else:
                delta = 1 - (2 * (1 - r)) ** (1 / (eta + 1))

            mutated[i] = individual[i] + delta * (bounds[i][1] - bounds[i][0])

            # 确保在边界内
            mutated[i] = np.clip(mutated[i], bounds[i][0], bounds[i][1])

        return mutated

    # 初始化种群
    dim = len(bounds)
    population = sampling.lhs(dim, pop_size, bounds[0][0], bounds[0][1])

    # 记录最优解
    best_solution = None
    best_fitness = float('-inf')  # 假设是最大化问题

    # 迭代优化
    for generation in range(n_generations):
        # 计算适应度
        fitness = np.zeros(pop_size)
        for i in range(pop_size):
            fitness[i] = obj_func(population[i])

        # 更新全局最优解
        current_best_idx = np.argmax(fitness)
        if fitness[current_best_idx] > best_fitness:
            best_fitness = fitness[current_best_idx]
            best_solution = population[current_best_idx].copy()

        # 创建新一代种群
        new_population = np.zeros_like(population)

        # 精英保留策略
        elite_indices = np.argsort(fitness)[-elite_size:]
        new_population[:elite_size] = population[elite_indices]

        # 生成剩余个体
        for i in range(elite_size, pop_size):
            # 选择父代 - 锦标赛选择
            parent1_idx = _tournament_selection(fitness, k=3)
            parent2_idx = _tournament_selection(fitness, k=3)

            # 交叉
            if np.random.random() < crossover_rate:
                child = _crossover(population[parent1_idx], population[parent2_idx])
            else:
                child = population[parent1_idx].copy()

            # 变异
            if np.random.random() < mutation_rate:
                child = _mutation(child, bounds)

            new_population[i] = child

        # 更新种群
        population = new_population

        # 输出当前代的最优解
        if generation % 10 == 0:
            print(f"代数: {generation}, 最优适应度: {best_fitness}")

    return best_solution, best_fitness

def STLBO(bounds, obj_func, pop_size=50, max_iterations=100, A1=0.5, A2=1.0):
    """
    改进的自适应教学-学习优化算法 (Self-adaptive Teaching-Learning-Based Optimization)

    参数:
        bounds: 搜索空间边界，形式为 [(x1_min, x1_max), (x2_min, x2_max), ...]
        obj_func: 目标函数，接受一个numpy数组作为输入并返回一个标量值
        pop_size: 种群大小 (N)
        max_iterations: 最大迭代次数 (t_1)
        A1: 算法参数A1，用于控制学习率
        A2: 算法参数A2，用于控制学习率

    返回:
        best_solution: 最优解
        best_fitness: 最优适应度值
    """
    # 获取问题维度
    dim = len(bounds)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    # 初始化种群
    population = sampling.lhs(dim, pop_size, bounds[0][0], bounds[0][1])
    for t in range(max_iterations):
        population = utils.toTensor(population).to(device=device)
        t1 = t+1
        TF = ((max_iterations-t1)/max_iterations)**2+2
        if t1 > max_iterations/2:
            fitness_list = obj_func(population)
            population = utils.tondarry(population)
            teacher_idx = np.argmin(fitness_list)
            teacher = population[teacher_idx]
            d = np.random.randint(0, dim)
            j = np.random.randint(0, pop_size)
            while j == teacher_idx:
                j = np.random.randint(0, pop_size)
            r = random.random()
            teacher = teacher + r*(teacher-population[j])
            population[teacher_idx] = teacher
            population = utils.toTensor(population).to(device=device)
        else:
            fitness_list = obj_func(population)
            population = utils.tondarry(population)
            teacher_idx = np.argmin(fitness_list)
            teacher = population[teacher_idx]
        population = utils.tondarry(population)
        S = [fitness_list[teacher_idx]/fitness_list[j] for j in range(pop_size)]
        for j in range(pop_size):
            for d in range(dim):
                Md = np.sum(population[:, d])/pop_size
                population[j][d]=A1*population[j][d]+S[j]*(teacher[d]-TF*Md)
        # 学习阶段
        for j in range(pop_size):
            k = np.random.randint(0, pop_size)
            while k == j: k = np.random.randint(0, pop_size)
            if fitness_list[j] < fitness_list[k]:
                population[j] = A2*population[j]+S[j]*(population[j]-population[k])+S[j]*(teacher-TF*population[j])
            else:
                population[j] = A2*population[j]+S[j]*(population[k]-population[j])+S[j]*(teacher-TF*population[j])
        for j in range(pop_size):
            for d in range(dim):
                population[j][d] = np.clip(population[j][d], bounds[d][0], bounds[d][1])
    population = utils.toTensor(population).to(device=device)
    fitness_list = obj_func(population)
    population = utils.tondarry(population)
    return population[np.argmin(fitness_list)], np.min(fitness_list)

def STLBO_numpy(bounds, obj_func, pop_size=50, max_iterations=100, A1=0.5, A2=1.0):
    """
    改进的自适应教学-学习优化算法 (Self-adaptive Teaching-Learning-Based Optimization)

    参数:
        bounds: 搜索空间边界，形式为 [(x1_min, x1_max), (x2_min, x2_max), ...]
        obj_func: 目标函数，接受一个numpy数组作为输入并返回一个标量值
        pop_size: 种群大小 (N)
        max_iterations: 最大迭代次数 (t_1)
        A1: 算法参数A1，用于控制学习率
        A2: 算法参数A2，用于控制学习率

    返回:
        best_solution: 最优解
        best_fitness: 最优适应度值
    """
    # 获取问题维度
    dim = len(bounds)
    # 初始化种群
    population = sampling.lhs(dim, pop_size, bounds[0][0], bounds[0][1])
    for t in range(max_iterations):        
        t1 = t+1
        TF = ((max_iterations-t1)/max_iterations)**2+2
        if t1 > max_iterations/2:
            fitness_list = obj_func(population)
            population = utils.tondarry(population)
            teacher_idx = np.argmin(fitness_list)
            teacher = population[teacher_idx]
            d = np.random.randint(0, dim)
            j = np.random.randint(0, pop_size)
            while j == teacher_idx:
                j = np.random.randint(0, pop_size)
            r = random.random()
            teacher = teacher + r*(teacher-population[j])
            population[teacher_idx] = teacher
        else:
            fitness_list = obj_func(population)
            population = utils.tondarry(population)
            teacher_idx = np.argmin(fitness_list)
            teacher = population[teacher_idx]
        population = utils.tondarry(population)
        S = [fitness_list[teacher_idx]/fitness_list[j] for j in range(pop_size)]
        for j in range(pop_size):
            for d in range(dim):
                Md = np.sum(population[:, d])/pop_size
                population[j][d]=A1*population[j][d]+S[j]*(teacher[d]-TF*Md)
        # 学习阶段
        for j in range(pop_size):
            k = np.random.randint(0, pop_size)
            while k == j: k = np.random.randint(0, pop_size)
            if fitness_list[j] < fitness_list[k]:
                population[j] = A2*population[j]+S[j]*(population[j]-population[k])+S[j]*(teacher-TF*population[j])
            else:
                population[j] = A2*population[j]+S[j]*(population[k]-population[j])+S[j]*(teacher-TF*population[j])
        for j in range(pop_size):
            for d in range(dim):
                population[j][d] = np.clip(population[j][d], bounds[d][0], bounds[d][1])
    fitness_list = obj_func(population)
    return population[np.argmin(fitness_list)], np.min(fitness_list)

def subsolv(m, n, epsimin, low, upp, alfa, beta, p0, q0, P, Q, a0, a, b, c, d):
    een = np.ones(n)
    eem = np.ones(m)
    epsi = 1
    epsvecn = epsi*een
    epsvecm = epsi*eem
    x = 0.5*(alfa+beta)
    y = eem
    z = 1
    lam = eem
    xsi = een/(x-alfa)
    xsi = np.max(xsi, een)
    eta = een/(beta-x)
    eta = max(eta, een)
    mu = np.max(eem, 0.5*c)
    zet = 1
    s = eem
    itera = 0
     



def mmasub(m, n, iter, xval, xmin, xmax, xold1, xold2, f0val, df0dx, fval, dfdx, low, upp, a0, a, c, d):
    epsimin = 1e-7
    raa0 = 0.00001
    move = 0.5
    albefa = 0.1
    asyinit = 0.5
    asyincr = 1.2
    asydecr = 0.7
    eeen = np.ones(n)
    eeem = np.ones(m)
    zeron = np.zeros(n)
    if iter < 2.5:
        low = xval - asyinit*(xmax-xmin)
        upp = xval + asyinit*(xmax-xmin)
        low1 = xval - 1*0.1
        upp1 = xval + 1*0.1
        
        low = np.max(low, low1)
        upp = np.min(upp, upp1)
    else:
        zzz = (xval-xold1)*(xold1-xold2)
        factor = eeen
        factor[zzz > 0] = asyincr
        factor[zzz < 0] = asydecr
        low = xval - factor * (xold1-low)
        upp = xval - factor * (upp - xold1)
        lowmin = xval - 10*(xmax-xmin)
        lowmax = xval - 0.01*(xmax-xmin)
        uppmin = xval + 0.01*(xmax-xmin)
        uppmax = xval + 10*(xmax-xmin)
        low = np.max(low, lowmin)
        low = np.min(low, lowmax)
        upp = np.min(upp, uppmax)
        upp = np.max(upp, uppmin)
    
    zzz1 = low+albefa*(xval-low)
    zzz2 = xval - move*(xmax-xmin)
    zzz = np.max(zzz1, zzz2)
    alfa = np.max(zzz, xmin)
    zzz1 = upp-albefa*(upp-xval)
    zzz2 = xval+move*(xmax-xmin)
    zzz = np.min(zzz1, zzz2)
    beta = np.min(zzz, xmax)

    xmami = xmax-xmin
    xmamieps = 0.00001*eeen
    xmai = np.max(xmami, xmamieps)
    xmamiinv = eeen/xmami
    ux1 = upp-xval
    ux2 = ux1*ux1
    xl1 = xval-low
    xl2 = xl1*xl1
    uxinv = eeen/ux1
    xlinv = eeen/xl1

    p0 = zeron
    q0 = zeron
    p0 = np.max(df0dx, 0)
    q0 = np.max(-df0dx, 0)
    pq0 = 0.001*(p0 + q0) + raa0@xmamiinv
    p0 = p0 + pq0
    q0 = q0 + pq0
    p0 = p0*ux2
    q0 = q0*xl2

    P = sparse.csr_matrix(shape=(m, n))
    Q = sparse.csr_matrix(shape=(m, n))
    P = np.max(dfdx, 0)
    Q = np.max(-dfdx, 0)
    PQ = 0.001*(P+Q)+raa0*eeem@xmamiinv
    P += PQ
    Q += PQ
    P = P @ sparse.diags(ux2, shape=(n, n), format='csr')
    Q = Q @ sparse.diags(xl2, shape=(n, n), format='csr')
    b = P @ uxinv + Q @ xlinv - fval
    xmma, ymma, zmma, lam, xsi, eta, mu, zet, s = subsolv(m, n, epsimin, low, upp, alfa, beta, p0, q0, P, Q, a0, a, b, c, d)
    
    return xmma, ymma, zmma, lam, xsi, eta, mu, zet, s, low, upp

def XMMA(x, o, do, st, dst, low, up, iter, xold1, xold2, lowi, upi):
    n = len(x)
    m0, n0 = dst.shape
    if n0 == n:
        m = m0
        dfdx = dst
    else:
        m = n0
        dfdx = dst.conj().T
    
    d = np.zeros(m)
    a0 = 1
    a = np.zeros(m)
    cc = 1000*np.ones(m)
    xmin = low
    xmax = up
    f0val = o
    df0dx=do
    fval = st
    xval = x
    xmma, ymma, zmma, lam, xsi, eta, mu, zet, s, lowi, upi = mmasub(m, n, iter, xval, xmin, xmax, xold1, xold2, f0val, df0dx, fval, dfdx, lowi, upi, a0, a, cc, d)
    xnew = xmma
    return xnew, lowi, upi