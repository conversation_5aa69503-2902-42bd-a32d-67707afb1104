"""
相关性函数，用于Kriging模型
"""

import numpy as np


def corrgauss(theta, d):
    """
    高斯相关性函数
    
    r_i = prod exp(-theta_j * d_ij^2), i = 1,...,m
           j=1
    
    参数:
    theta : 相关函数中的参数，如果长度为1，则为各向同性模型
    d : m*n矩阵，包含给定数据点之间的差异
    
    返回:
    r : 相关性
    dr : r在x处的雅可比矩阵(如果需要)
    """
    m, n = d.shape  # 差异数量和数据的维度
    
    if len(theta) == 1:
        theta = np.repeat(theta, n)
    elif len(theta) != n:
        raise ValueError(f"theta的长度必须为1或{n}")
    
    td = d**2 * (-theta.reshape(1, -1))
    r = np.exp(np.sum(td, axis=1))
    
    return r


def correxp(theta, d):
    """
    指数相关性函数
    
    r_i = prod exp(-theta_j * |d_ij|), i = 1,...,m
           j=1
    
    参数:
    theta : 相关函数中的参数，如果长度为1，则为各向同性模型
    d : m*n矩阵，包含给定数据点之间的差异
    
    返回:
    r : 相关性
    """
    m, n = d.shape
    
    if len(theta) == 1:
        theta = np.repeat(theta, n)
    elif len(theta) != n:
        raise ValueError(f"theta的长度必须为1或{n}")
    
    td = np.abs(d) * (-theta.reshape(1, -1))
    r = np.exp(np.sum(td, axis=1))
    
    return r


def corrcubic(theta, d):
    """
    立方相关性函数
    
    参数:
    theta : 相关函数中的参数，如果长度为1，则为各向同性模型
    d : m*n矩阵，包含给定数据点之间的差异
    
    返回:
    r : 相关性
    """
    m, n = d.shape
    
    if len(theta) == 1:
        theta = np.repeat(theta, n)
    elif len(theta) != n:
        raise ValueError(f"theta的长度必须为1或{n}")
    
    r = np.ones(m)
    for i in range(n):
        h = theta[i] * np.abs(d[:, i])
        idx = h <= 0.5
        r[idx] = r[idx] * (1 - 6*h[idx]**2 + 6*h[idx]**3)
        idx = (h > 0.5) & (h < 1)
        r[idx] = r[idx] * (2 * (1-h[idx])**3)
        idx = h >= 1
        r[idx] = 0
    
    return r


def corrspline(theta, d):
    """
    样条相关性函数
    
    参数:
    theta : 相关函数中的参数，如果长度为1，则为各向同性模型
    d : m*n矩阵，包含给定数据点之间的差异
    
    返回:
    r : 相关性
    """
    m, n = d.shape
    
    if len(theta) == 1:
        theta = np.repeat(theta, n)
    elif len(theta) != n:
        raise ValueError(f"theta的长度必须为1或{n}")
    
    r = np.ones(m)
    for i in range(n):
        h = theta[i] * np.abs(d[:, i])
        idx = h < 1
        r[idx] = r[idx] * (1 - 15*h[idx]**2 + 30*h[idx]**3)
        idx = (h >= 1) & (h < 2)
        r[idx] = r[idx] * 1.25 * (2 - h[idx])**3
        idx = h >= 2
        r[idx] = 0
    
    return r 