import numpy as np
from optimization_algorithm import STLBO
from optimization_algorithm import ga
import sampling
from surrogate_model import Surrogate
from surrogate_model import SAE
import matplotlib.pyplot as plt
from matplotlib import cm
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401
import utils
import torch
import benchmark

# def fitness(x):
#     if x.ndim == 1:
#         x = x.reshape(1, -1)
#     dim = x.shape[1]
#     return np.sum(x**2, axis=1)/4000-np.prod(np.cos(x/np.sqrt(np.arange(1, dim+1))), axis=1)+1

if __name__ == "__main__":
    np.random.seed(123)
    # def fitness(x):
    #     return (2+4*x[:, 0]+4*x[:, 1]-x[:, 0]**2-x[:, 1]**2+2*np.sin(2*x[:, 0])*np.sin(2*x[:, 1]))
    
    fitness = benchmark.ackley
    # 定义搜索空间边界
    dim = 2
    n = 400
    bounds = [[-3, 3]for _ in range(dim)]
    bounds = np.array(bounds)
    rim = (bounds[:, 1] - bounds[:, 0])/2

    abs_lower = np.array([-3]*dim)
    abs_upper = np.array([3]*dim)
    before = np.array([-3]*dim)
    center_point = sampling.lhs(dim, 1, abs_lower, abs_upper)
    best = center_point
    # model.plot_comparison(fitness, X, Y)
    history = []
    totol_points = 0
    before_optimal = 1000
    cur_optimal = 100
    y_min = 1e-3
    while np.max(np.abs(before-best)) > 0.01:
        lower_bound = np.maximum(center_point-rim, abs_lower)
        upper_bound = np.minimum(center_point+rim, abs_upper)
        X = sampling.lhs(dim, n, lower_bound, upper_bound)
        Y = fitness(X)
        X = utils.toTensor(X)
        Y = utils.toTensor(Y)

        train_x = X[:int(n*0.9), :]
        train_y = Y[:int(n*0.9)]
        
        valid_x = X[int(n*0.9):, :]
        valid_y = Y[int(n*0.9):]

        model = Surrogate(dim, 1, 1000, 5)
        totol_points += n
        #loss, max_pix = model.valid_model(test_x, test_y)
        add_num = 0
        loss = 1
        new_bounds = np.vstack((lower_bound, upper_bound)).T
        while loss > 0.01:
            loss, _ = model.valid_model(valid_x, valid_y)
            print(loss)
            model.train(train_x, train_y, epochs=1500)
        
        solution, fitness_val = STLBO(
            bounds=new_bounds,
            obj_func=model.predict,
            pop_size=100,
            max_iterations=100,
            A1=0.8,
            A2=0.7)
        
        history_part = [solution]
        solution_part = [solution]
        before_optimal = fitness_val
        cur_optimal = fitness_val
        y_min = fitness_val

        while add_num < 50 or cur_optimal-before_optimal > 0.01*y_min:
            loss, max_pix = model.valid_model(valid_x, valid_y)
            max_pix = utils.tondarry(max_pix)
            new_point = sampling.lhs(dim, 1, solution-1e-3, solution+1e-3)
            add_num += 1
            new_y = fitness(new_point)
            new_point = utils.toTensor(new_point)
            new_y = utils.toTensor(new_y)
            train_x = torch.cat((train_x, utils.toTensor(new_point)), dim=0)
            train_y = torch.cat((train_y, utils.toTensor(new_y)), dim=0)
            model.train(train_x, train_y, epochs=1500)
            solution, fitness_val = STLBO(
                bounds=new_bounds,
                obj_func=model.predict,
                pop_size=100,
                max_iterations=100,
                A1=0.8,
                A2=0.7)
            real_val = fitness(solution)
            history_part.append(real_val)
            solution_part.append(solution)
            before_optimal = cur_optimal
            cur_optimal = fitness_val
            # model.plot_comparison(fitness, X, Y)
            y_min = np.min(np.array(history_part))

        history.append(history_part)
        center_point = solution_part[np.argmin(np.array(history_part))]
        rim = 0.9*rim
        before = best
        best = solution_part[np.argmin(np.array(history_part))]
        totol_points += add_num

        print(f"real_val: {real_val}, fitness_val: {fitness_val}, solution: {solution}")
        
    colors = ['blue', 'orange', 'green', 'purple', 'red', 'cyan', 'magenta', 'yellow']

    all_x = []
    all_y = []
    start_idx = 0

    for i, sublist in enumerate(history):
        length = len(sublist)
        x = np.arange(start_idx, start_idx + length)
        plt.plot(x, sublist, color=colors[i % len(colors)], linewidth=1.5)
        if i < len(history) - 1:
            plt.plot([start_idx + length - 1, start_idx + length], 
                    [sublist[-1], history[i+1][0]], 
                    'k-', alpha=0.5)        
        start_idx += length

    plt.xlabel('Number of FE evaluations')
    plt.ylabel('f(x)')
    plt.grid(True, alpha=0.3)
    plt.ylim(bottom=0)  # 设置y轴从0开始
    plt.tight_layout()
    plt.show()


    print(f"最终用于训练的点数量：{totol_points}")
    print("\n所有运行的最优结果:")
    print(f"最优解: {solution}")
    print(f"最优适应度值: {fitness_val}")
