import numpy as np
from scipy import sparse
from scipy.sparse import spdiags
from scipy.sparse.linalg import spsolve

def mmasub(m, n, iter, xval, xmin, xmax, xold1, xold2, 
          f0val, df0dx, fval, dfdx, low, upp, a0, a, c, d):
    """
    执行一次MMA迭代，旨在解决非线性规划问题:
         
    最小化   f_0(x) + a_0*z + sum( c_i*y_i + 0.5*d_i*(y_i)^2 )
    受约束   f_i(x) - a_i*z - y_i <= 0,  i = 1,...,m
            xmin_j <= x_j <= xmax_j,    j = 1,...,n
            z >= 0,   y_i >= 0,         i = 1,...,m
            
    输入参数:
    ----------
    m     : 通用约束的数量
    n     : 变量x_j的数量
    iter  : 当前迭代次数（首次调用mmasub时=1）
    xval  : 列向量，当前变量x_j的值
    xmin  : 列向量，变量x_j的下界
    xmax  : 列向量，变量x_j的上界
    xold1 : 上一次迭代的xval（前提是iter>1）
    xold2 : 两次迭代前的xval（前提是iter>2）
    f0val : 目标函数f_0在xval处的值
    df0dx : 目标函数f_0对变量x_j的导数的列向量，在xval处计算
    fval  : 约束函数f_i的值的列向量，在xval处计算
    dfdx  : 约束函数f_i对变量x_j的导数的(m x n)矩阵，在xval处计算
            dfdx[i,j]是f_i对x_j的导数
    low   : 列向量，上一次迭代的下渐近线（前提是iter>1）
    upp   : 列向量，上一次迭代的上渐近线（前提是iter>1）
    a0    : 项a_0*z中的常数a_0
    a     : 列向量，项a_i*z中的常数a_i
    c     : 列向量，项c_i*y_i中的常数c_i
    d     : 列向量，项0.5*d_i*(y_i)^2中的常数d_i
    
    输出参数:
    ----------
    xmma  : 列向量，当前MMA子问题中变量x_j的最优值
    ymma  : 列向量，当前MMA子问题中变量y_i的最优值
    zmma  : 标量，当前MMA子问题中变量z的最优值
    lam   : m个通用MMA约束的拉格朗日乘子
    xsi   : n个约束alfa_j - x_j <= 0的拉格朗日乘子
    eta   : n个约束x_j - beta_j <= 0的拉格朗日乘子
    mu    : m个约束-y_i <= 0的拉格朗日乘子
    zet   : 单个约束-z <= 0的拉格朗日乘子
    s     : m个通用MMA约束的松弛变量
    low   : 列向量，在当前MMA子问题中计算并使用的下渐近线
    upp   : 列向量，在当前MMA子问题中计算并使用的上渐近线
    """
    
    epsimin = 10**(-7)
    raa0 = 0.00001
    move = 0.5
    albefa = 0.1
    asyinit = 0.5
    asyincr = 1.2
    asydecr = 0.7
    
    # 将输入转换为numpy数组确保类型一致
    xval = np.asarray(xval, dtype=np.float64).reshape(-1)
    xmin = np.asarray(xmin, dtype=np.float64).reshape(-1)
    xmax = np.asarray(xmax, dtype=np.float64).reshape(-1)
    xold1 = np.asarray(xold1, dtype=np.float64).reshape(-1) if xold1 is not None else None
    xold2 = np.asarray(xold2, dtype=np.float64).reshape(-1) if xold2 is not None else None
    df0dx = np.asarray(df0dx, dtype=np.float64).reshape(-1)
    fval = np.asarray(fval, dtype=np.float64).reshape(-1)
    dfdx = np.asarray(dfdx, dtype=np.float64).reshape(m, n)
    low = np.asarray(low, dtype=np.float64).reshape(-1) if low is not None else None
    upp = np.asarray(upp, dtype=np.float64).reshape(-1) if upp is not None else None
    a = np.asarray(a, dtype=np.float64).reshape(-1) if a is not None else np.zeros(m)
    c = np.asarray(c, dtype=np.float64).reshape(-1) if c is not None else np.ones(m)*1000
    d = np.asarray(d, dtype=np.float64).reshape(-1) if d is not None else np.zeros(m)
    
    eeen = np.ones(n)
    eeem = np.ones(m)
    zeron = np.zeros(n)

    # 计算渐近线low和upp
    if iter < 2.5:
        # 原始代码
        #low = xval - asyinit*(xmax-xmin)
        #upp = xval + asyinit*(xmax-xmin)
        
        # 修改后的代码
        low1 = xval - 1 * 0.05
        upp1 = xval + 1 * 0.05
        
        if low is None:
            low = low1
        else:
            low = np.maximum(low, low1)
            
        if upp is None:
            upp = upp1
        else:
            upp = np.minimum(upp, upp1)
    else:
        zzz = (xval - xold1) * (xold1 - xold2)
        factor = np.ones_like(eeen)
        factor[zzz > 0] = asyincr
        factor[zzz < 0] = asydecr
        low = xval - factor * (xold1 - low)
        upp = xval + factor * (upp - xold1)
        lowmin = xval - 10 * (xmax - xmin)
        lowmax = xval - 0.01 * (xmax - xmin)
        uppmin = xval + 0.01 * (xmax - xmin)
        uppmax = xval + 10 * (xmax - xmin)
        low = np.maximum(low, lowmin)
        low = np.minimum(low, lowmax)
        upp = np.minimum(upp, uppmax)
        upp = np.maximum(upp, uppmin)

    # 计算界限alfa和beta
    zzz1 = low + albefa * (xval - low)
    zzz2 = xval - move * (xmax - xmin)
    zzz = np.maximum(zzz1, zzz2)
    alfa = np.maximum(zzz, xmin)
    
    zzz1 = upp - albefa * (upp - xval)
    zzz2 = xval + move * (xmax - xmin)
    zzz = np.minimum(zzz1, zzz2)
    beta = np.minimum(zzz, xmax)

    # 计算p0, q0, P, Q和b
    xmami = xmax - xmin
    xmamieps = 0.00001 * eeen
    xmami = np.maximum(xmami, xmamieps)
    xmamiinv = eeen / xmami
    
    ux1 = upp - xval
    ux2 = ux1 * ux1
    xl1 = xval - low
    xl2 = xl1 * xl1
    uxinv = eeen / ux1
    xlinv = eeen / xl1

    p0 = np.zeros_like(zeron)
    q0 = np.zeros_like(zeron)
    p0 = np.maximum(df0dx, 0)
    q0 = np.maximum(-df0dx, 0)
    pq0 = 0.001 * (p0 + q0) + raa0 * xmamiinv
    p0 = p0 + pq0
    q0 = q0 + pq0
    p0 = p0 * ux2
    q0 = q0 * xl2

    P = np.maximum(dfdx, 0)
    Q = np.maximum(-dfdx, 0)
    
    # 使用numpy广播代替MATLAB的矩阵运算
    PQ = 0.001 * (P + Q) + raa0 * np.outer(eeem, xmamiinv)
    P = P + PQ
    Q = Q + PQ
    
    # 转换为稀疏矩阵
    P_sparse = sparse.csr_matrix(P)
    Q_sparse = sparse.csr_matrix(Q)
    
    P_sparse = P_sparse.multiply(sparse.diags(ux2))
    Q_sparse = Q_sparse.multiply(sparse.diags(xl2))
    
    b = P_sparse @ uxinv + Q_sparse @ xlinv - fval

    # 使用子问题求解器来解决子问题
    xmma, ymma, zmma, lam, xsi, eta, mu, zet, s = subsolv(
        m, n, epsimin, low, upp, alfa, beta, p0, q0, P_sparse, Q_sparse, a0, a, b, c, d
    )

    return xmma, ymma, zmma, lam, xsi, eta, mu, zet, s, low, upp


def subsolv(m, n, epsimin, low, upp, alfa, beta, p0, q0, P, Q, a0, a, b, c, d):
    """
    该函数subsolv解决MMA子问题:
    
    最小化   SUM[ p0j/(uppj-xj) + q0j/(xj-lowj) ] + a0*z +
            + SUM[ ci*yi + 0.5*di*(yi)^2 ],
    
    受约束   SUM[ pij/(uppj-xj) + qij/(xj-lowj) ] - ai*z - yi <= bi,
            alfaj <=  xj <=  betaj,  yi >= 0,  z >= 0.
    
    输入参数:
    ----------
    m, n, low, upp, alfa, beta, p0, q0, P, Q, a0, a, b, c, d
    
    输出参数:
    ----------
    xmma, ymma, zmma, 松弛变量和拉格朗日乘子
    """
    
    een = np.ones(n)
    eem = np.ones(m)
    epsi = 1
    epsvecn = epsi * een
    epsvecm = epsi * eem
    
    x = 0.5 * (alfa + beta)
    y = eem.copy()
    z = 1
    lam = eem.copy()
    
    xsi = een / (x - alfa)
    xsi = np.maximum(xsi, een)
    eta = een / (beta - x)
    eta = np.maximum(eta, een)
    mu = np.maximum(eem, 0.5 * c)
    zet = 1
    s = eem.copy()
    
    itera = 0
    
    # 主循环
    while epsi > epsimin:
        epsvecn = epsi * een
        epsvecm = epsi * eem
        
        ux1 = upp - x
        xl1 = x - low
        ux2 = ux1 * ux1
        xl2 = xl1 * xl1
        
        uxinv1 = een / ux1
        xlinv1 = een / xl1
        
        plam = p0 + P.T @ lam
        qlam = q0 + Q.T @ lam
        
        gvec = P @ uxinv1 + Q @ xlinv1
        
        dpsidx = plam / ux2 - qlam / xl2
        rex = dpsidx - xsi + eta
        rey = c + d * y - mu - lam
        rez = a0 - zet - a.T @ lam
        relam = gvec - a * z - y + s - b
        rexsi = xsi * (x - alfa) - epsvecn
        reeta = eta * (beta - x) - epsvecn
        remu = mu * y - epsvecm
        rezet = zet * z - epsi
        res = lam * s - epsvecm
        
        residu1 = np.concatenate([rex, rey, [rez]])
        residu2 = np.concatenate([relam, rexsi, reeta, remu, [rezet], res])
        residu = np.concatenate([residu1, residu2])
        
        residunorm = np.sqrt(np.sum(residu**2))
        residumax = np.max(np.abs(residu))
        
        ittt = 0
        
        # 牛顿法求解非线性方程
        while residumax > 0.9 * epsi and ittt < 200:
            ittt += 1
            itera += 1
            
            ux1 = upp - x
            xl1 = x - low
            ux2 = ux1 * ux1
            xl2 = xl1 * xl2
            ux3 = ux1 * ux2
            xl3 = xl1 * xl2
            
            uxinv1 = een / ux1
            xlinv1 = een / xl1
            uxinv2 = een / ux2
            xlinv2 = een / xl2
            
            plam = p0 + P.T @ lam
            qlam = q0 + Q.T @ lam
            
            gvec = P @ uxinv1 + Q @ xlinv1
            
            # 计算GG矩阵
            GG = P.multiply(sparse.diags(uxinv2)) - Q.multiply(sparse.diags(xlinv2))
            
            dpsidx = plam / ux2 - qlam / xl2
            delx = dpsidx - epsvecn / (x - alfa) + epsvecn / (beta - x)
            dely = c + d * y - lam - epsvecm / y
            delz = a0 - a.T @ lam - epsi / z
            dellam = gvec - a * z - y - b + epsvecm / lam
            
            diagx = plam / ux3 + qlam / xl3
            diagx = 2 * diagx + xsi / (x - alfa) + eta / (beta - x)
            diagxinv = een / diagx
            
            diagy = d + mu / y
            diagyinv = eem / diagy
            diaglam = s / lam
            diaglamyi = diaglam + diagyinv
            
            # 解线性方程组
            if m < n:
                blam = dellam + dely / diagy - GG @ (delx / diagx)
                bb = np.concatenate([blam, [delz]])
                
                Alam = sparse.diags(diaglamyi) + GG @ sparse.diags(diagxinv) @ GG.T
                
                # 构建增广矩阵
                AA_top = sparse.hstack([Alam, sparse.csr_matrix(a.reshape(-1, 1))])
                AA_bottom = sparse.hstack([sparse.csr_matrix(a.reshape(1, -1)), sparse.csr_matrix([[-zet/z]])])
                AA = sparse.vstack([AA_top, AA_bottom])
                
                # 求解线性方程组
                solut = spsolve(AA, bb)
                
                dlam = solut[:m]
                dz = solut[m]
                dx = -delx / diagx - (GG.T @ dlam) / diagx
                
            else:
                diaglamyiinv = eem / diaglamyi
                dellamyi = dellam + dely / diagy
                
                # 转换为numpy数组进行操作
                Axx = sparse.diags(diagx) + GG.T @ sparse.diags(diaglamyiinv) @ GG
                azz = zet / z + a.T @ (a / diaglamyi)
                axz = -GG.T @ (a / diaglamyi)
                
                # 构建增广矩阵
                AA_top = sparse.hstack([Axx, sparse.csr_matrix(axz.reshape(-1, 1))])
                AA_bottom = sparse.hstack([sparse.csr_matrix(axz.reshape(1, -1)), sparse.csr_matrix([[azz]])])
                AA = sparse.vstack([AA_top, AA_bottom])
                
                bx = delx + GG.T @ (dellamyi / diaglamyi)
                bz = delz - a.T @ (dellamyi / diaglamyi)
                
                bb = np.concatenate([[-bx], [-bz]])
                
                # 求解线性方程组
                solut = spsolve(AA, bb)
                
                dx = solut[:n]
                dz = solut[n]
                dlam = (GG @ dx) / diaglamyi - dz * (a / diaglamyi) + dellamyi / diaglamyi
                
            # 计算其它变量的增量
            dy = -dely / diagy + dlam / diagy
            dxsi = -xsi + epsvecn / (x - alfa) - (xsi * dx) / (x - alfa)
            deta = -eta + epsvecn / (beta - x) + (eta * dx) / (beta - x)
            dmu = -mu + epsvecm / y - (mu * dy) / y
            dzet = -zet + epsi / z - zet * dz / z
            ds = -s + epsvecm / lam - (s * dlam) / lam
            
            # 合并所有变量
            xx = np.concatenate([y, [z], lam, xsi, eta, mu, [zet], s])
            dxx = np.concatenate([dy, [dz], dlam, dxsi, deta, dmu, [dzet], ds])
            
            # 计算步长
            stepxx = -1.01 * dxx / xx
            stmxx = np.max(stepxx)
            stepalfa = -1.01 * dx / (x - alfa)
            stmalfa = np.max(stepalfa)
            stepbeta = 1.01 * dx / (beta - x)
            stmbeta = np.max(stepbeta)
            stmalbe = max(stmalfa, stmbeta)
            stmalbexx = max(stmalbe, stmxx)
            stminv = max(stmalbexx, 1)
            steg = 1 / stminv
            
            # 保存旧值
            xold = x.copy()
            yold = y.copy()
            zold = z
            lamold = lam.copy()
            xsiold = xsi.copy()
            etaold = eta.copy()
            muold = mu.copy()
            zetold = zet
            sold = s.copy()
            
            # 线搜索
            itto = 0
            resinew = 2 * residunorm
            while resinew > residunorm and itto < 50:
                itto += 1
                x = xold + steg * dx
                y = yold + steg * dy
                z = zold + steg * dz
                lam = lamold + steg * dlam
                xsi = xsiold + steg * dxsi
                eta = etaold + steg * deta
                mu = muold + steg * dmu
                zet = zetold + steg * dzet
                s = sold + steg * ds
                
                # 重新计算残差
                ux1 = upp - x
                xl1 = x - low
                ux2 = ux1 * ux1
                xl2 = xl1 * xl1
                
                uxinv1 = een / ux1
                xlinv1 = een / xl1
                
                plam = p0 + P.T @ lam
                qlam = q0 + Q.T @ lam
                
                gvec = P @ uxinv1 + Q @ xlinv1
                
                dpsidx = plam / ux2 - qlam / xl2
                rex = dpsidx - xsi + eta
                rey = c + d * y - mu - lam
                rez = a0 - zet - a.T @ lam
                relam = gvec - a * z - y + s - b
                rexsi = xsi * (x - alfa) - epsvecn
                reeta = eta * (beta - x) - epsvecn
                remu = mu * y - epsvecm
                rezet = zet * z - epsi
                res = lam * s - epsvecm
                
                residu1 = np.concatenate([rex, rey, [rez]])
                residu2 = np.concatenate([relam, rexsi, reeta, remu, [rezet], res])
                residu = np.concatenate([residu1, residu2])
                
                resinew = np.sqrt(np.sum(residu**2))
                steg = steg / 2
                
            # 更新残差
            residunorm = resinew
            residumax = np.max(np.abs(residu))
            steg = 2 * steg
            
        # 减小扰动参数
        epsi = 0.1 * epsi
    
    # 返回最优值
    xmma = x
    ymma = y
    zmma = z
    lamma = lam
    xsimma = xsi
    etamma = eta
    mumma = mu
    zetmma = zet
    smma = s
    
    return xmma, ymma, zmma, lamma, xsimma, etamma, mumma, zetmma, smma


# 完整的MMA优化器
class MMAOptimizer:
    """
    MMA(移动渐近线法)优化器类
    
    参数:
    ----------
    n : 设计变量的数量
    m : 约束的数量
    xmin : 设计变量下界
    xmax : 设计变量上界
    a0 : a0*z项中的常数
    a : 约束中a_i*z项中的常数向量
    c : 项c_i*y_i中的常数向量
    d : 项0.5*d_i*(y_i)^2中的常数向量
    move_limit : 移动极限(可选)
    """
    
    def __init__(self, n, m, xmin, xmax, a0=1.0, a=None, c=None, d=None):
        self.n = n
        self.m = m
        self.xmin = np.asarray(xmin).reshape(-1)
        self.xmax = np.asarray(xmax).reshape(-1)
        self.a0 = a0
        self.a = np.ones(m) if a is None else np.asarray(a).reshape(-1)
        self.c = np.ones(m)*1000 if c is None else np.asarray(c).reshape(-1)
        self.d = np.zeros(m) if d is None else np.asarray(d).reshape(-1)
        
        self.iter = 0
        self.xold1 = None
        self.xold2 = None
        self.low = None
        self.upp = None
        
    def update(self, x, f0val, df0dx, fval, dfdx):
        """
        执行一次MMA更新
        
        参数:
        ----------
        x : 当前设计变量
        f0val : 目标函数值
        df0dx : 目标函数梯度
        fval : 约束函数值
        dfdx : 约束函数梯度
        
        返回:
        ----------
        x_new : 更新后的设计变量
        """
        self.iter += 1
        
        # 第一次迭代
        if self.iter == 1:
            self.xold1 = x.copy()
            self.xold2 = x.copy()
        
        # 保存历史
        elif self.iter > 1:
            self.xold2 = self.xold1.copy()
            self.xold1 = x.copy()
        
        # 执行MMA更新
        xmma, ymma, zmma, lam, xsi, eta, mu, zet, s, self.low, self.upp = mmasub(
            self.m, self.n, self.iter, x, self.xmin, self.xmax, self.xold1, self.xold2,
            f0val, df0dx, fval, dfdx, self.low, self.upp, self.a0, self.a, self.c, self.d
        )
        
        return xmma 