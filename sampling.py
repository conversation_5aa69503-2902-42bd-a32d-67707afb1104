import numpy as np
from utils import toColVec

def randomSampling(dim, n, lower_bound, upper_bound):
    samplePoints = np.random.random([dim, n])
    samplePoints = lower_bound +  samplePoints.T * (upper_bound - lower_bound)
    return samplePoints.T

def lhs(dim, n, lower_bound, upper_bound):
    intervalSize = 1.0 / n
    samplePoints = np.empty([dim, n])
    for i in range(n):
        samplePoints[:, i] = np.random.uniform(low=i * intervalSize, high=(i + 1) * intervalSize, size=dim)
    for i in range(dim):
        np.random.shuffle(samplePoints[i])
    samplePoints = lower_bound +  samplePoints.T * (upper_bound - lower_bound)
    return samplePoints


def averageSampling(dim, n, lower_bound, upper_bound):
    """
    在每个维度均匀分布n个点

    参数:
        dim: 维度
        n: 采样点数量
        lower_bound: 各维度的下界
        upper_bound: 各维度的上界

    返回:
        shape为(n, dim)的采样点数组
    """
    lower_bound, upper_bound = toColVec(lower_bound), toColVec(upper_bound)

    # 计算每个维度上需要多少个点
    # 对于d维空间中的n个点，我们希望每个维度上有大约n^(1/d)个点
    points_per_dim = int(np.ceil(n**(1/dim)))

    # 创建网格点
    grid_points = []
    for i in range(dim):
        # 在每个维度上创建均匀分布的点
        grid_points.append(np.linspace(lower_bound[i], upper_bound[i], points_per_dim))
# [1, 2, 3]
    # 使用meshgrid创建所有组合
    mesh = np.meshgrid(*grid_points)

    # 将网格点转换为样本点列表
    # 将每个维度的坐标展平并堆叠
    samplePoints = np.vstack([m.flatten() for m in mesh]).T

    # 如果生成的点数超过n，随机选择n个点
    if len(samplePoints) > n:
        indices = np.random.choice(len(samplePoints), n, replace=False)
        samplePoints = samplePoints[indices]

    return samplePoints

def lhsamp(m, n):
    """
    拉丁超立方采样
    
    参数:
    m : 样本点数量
    n : 维度数量
    
    返回:
    S : m行n列的采样点数组，每个值位于[0,1]范围内
    """
    S = np.zeros((m, n))
    for i in range(n):
        S[:, i] = (np.random.rand(m) + np.random.permutation(m)) / m
    return S