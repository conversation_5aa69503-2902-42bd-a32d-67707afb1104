import numpy as np
import torch

def toRowVec(x):
    if not isinstance(x, np.ndarray):
        x = np.array(x)
    if x.ndim == 1:
        return x
    return x.reshape(1, -1)

def toColVec(x):
    if not isinstance(x, np.ndarray):
        x = np.array(x)
    if x.ndim == 1:
        return x
    return x.reshape(-1, 1)

def split_dataset(dataset, train_rate=0.8):
    n = len(dataset)
    n_train = int(train_rate * n)
    n_val = n - n_train
    return torch.utils.data.random_split(
        dataset,
        [n_train, n_val]
    )

def toTensor(x):
    if type(x) == np.ndarray:
        return torch.from_numpy(x)
    return x.clone().detach()

def tondarry(x):
    if type(x) == torch.Tensor:
        return x.cpu().detach().numpy()
    return np.array(x)